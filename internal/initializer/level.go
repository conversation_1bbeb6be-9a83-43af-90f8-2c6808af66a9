package initializer

import (
	"context"
	"fmt"
	"log"

	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

func InitializeAgentLevels() error {
	levels := []model.AgentLevel{
		// Lv1: $0-$10,000 / $0-900,000 - Entry level agent
		{
			ID:                      1,
			Name:                    "Lv1",
			MemeVolumeThreshold:     decimal.NewFromFloat(0),
			ContractVolumeThreshold: decimal.NewFromFloat(0),
			MemeFeeRate:             decimal.NewFromFloat(0.009),  // 0.90%
			TakerFeeRate:            decimal.NewFromFloat(0.0001), // 0.01%
			MakerFeeRate:            decimal.NewFromFloat(0.0001), // 0.01%
			DirectCommissionRate:    decimal.NewFromFloat(0.10),   // 10% agent referral commission
			IndirectCommissionRate:  decimal.NewFromFloat(0.03),   // 3% agent referral commission
			ExtendedCommissionRate:  decimal.NewFromFloat(0.01),   // 1% agent referral commission
			ActivityCashbackRate:    decimal.NewFromFloat(0.00),   // 0% activity cashback
		},
		// Lv2: $10,000-$30,000 / $900,000-$2.7M
		{
			ID:                      2,
			Name:                    "Lv2",
			MemeVolumeThreshold:     decimal.NewFromFloat(10000),
			ContractVolumeThreshold: decimal.NewFromFloat(900000),
			MemeFeeRate:             decimal.NewFromFloat(0.009),  // 0.90%
			TakerFeeRate:            decimal.NewFromFloat(0.0001), // 0.01%
			MakerFeeRate:            decimal.NewFromFloat(0.0001), // 0.01%
			DirectCommissionRate:    decimal.NewFromFloat(0.20),   // 20% agent referral commission
			IndirectCommissionRate:  decimal.NewFromFloat(0.04),   // 4% agent referral commission
			ExtendedCommissionRate:  decimal.NewFromFloat(0.02),   // 2% agent referral commission
			ActivityCashbackRate:    decimal.NewFromFloat(0.05),   // 5% activity cashback
		},
		// Lv3: $30,000-$100,000 / $2.7M-$9M
		{
			ID:                      3,
			Name:                    "Lv3",
			MemeVolumeThreshold:     decimal.NewFromFloat(30000),
			ContractVolumeThreshold: decimal.NewFromFloat(2700000),
			MemeFeeRate:             decimal.NewFromFloat(0.009),  // 0.90%
			TakerFeeRate:            decimal.NewFromFloat(0.0001), // 0.01%
			MakerFeeRate:            decimal.NewFromFloat(0.0001), // 0.01%
			DirectCommissionRate:    decimal.NewFromFloat(0.30),   // 30%
			IndirectCommissionRate:  decimal.NewFromFloat(0.05),   // 5%
			ExtendedCommissionRate:  decimal.NewFromFloat(0.025),  // 2.5%
			ActivityCashbackRate:    decimal.NewFromFloat(0.10),   // 10%
		},
		// Lv4: $100,000-$300,000 / $9M-$27M
		{
			ID:                      4,
			Name:                    "Lv4",
			MemeVolumeThreshold:     decimal.NewFromFloat(100000),
			ContractVolumeThreshold: decimal.NewFromFloat(9000000),
			MemeFeeRate:             decimal.NewFromFloat(0.009),  // 0.90%
			TakerFeeRate:            decimal.NewFromFloat(0.0001), // 0.01%
			MakerFeeRate:            decimal.NewFromFloat(0.0001), // 0.01%
			DirectCommissionRate:    decimal.NewFromFloat(0.40),   // 40%
			IndirectCommissionRate:  decimal.NewFromFloat(0.06),   // 6%
			ExtendedCommissionRate:  decimal.NewFromFloat(0.03),   // 3%
			ActivityCashbackRate:    decimal.NewFromFloat(0.15),   // 15%
		},
		// Lv5: $300,000-$1M / $27M-$90M
		{
			ID:                      5,
			Name:                    "Lv5",
			MemeVolumeThreshold:     decimal.NewFromFloat(300000),
			ContractVolumeThreshold: decimal.NewFromFloat(27000000),
			MemeFeeRate:             decimal.NewFromFloat(0.009),  // 0.90%
			TakerFeeRate:            decimal.NewFromFloat(0.0001), // 0.01%
			MakerFeeRate:            decimal.NewFromFloat(0.0001), // 0.01%
			DirectCommissionRate:    decimal.NewFromFloat(0.50),   // 50%
			IndirectCommissionRate:  decimal.NewFromFloat(0.07),   // 7%
			ExtendedCommissionRate:  decimal.NewFromFloat(0.04),   // 4%
			ActivityCashbackRate:    decimal.NewFromFloat(0.20),   // 20%
		},
		// Lv∞ (first instance): $1M-$30M / $90M-$270M (参考)
		{
			ID:                      6,
			Name:                    "Lv∞",
			MemeVolumeThreshold:     decimal.NewFromFloat(1000000),
			ContractVolumeThreshold: decimal.NewFromFloat(90000000),
			MemeFeeRate:             decimal.NewFromFloat(0.009),  // 0.90%
			TakerFeeRate:            decimal.NewFromFloat(0.0001), // 0.01%
			MakerFeeRate:            decimal.NewFromFloat(0.0001), // 0.01%
			DirectCommissionRate:    decimal.NewFromFloat(0.60),   // 60% (灵活调整)
			IndirectCommissionRate:  decimal.NewFromFloat(0.08),   // 8% (灵活调整)
			ExtendedCommissionRate:  decimal.NewFromFloat(0.05),   // 5% (灵活调整)
			ActivityCashbackRate:    decimal.NewFromFloat(0.25),   // 25% (灵活调整)
		},
		// Lv∞ (second instance): $30M-$100M / $270M-$9B (参考)
		{
			ID:                      7,
			Name:                    "Lv∞",
			MemeVolumeThreshold:     decimal.NewFromFloat(30000000),
			ContractVolumeThreshold: decimal.NewFromFloat(270000000),
			MemeFeeRate:             decimal.NewFromFloat(0.009),  // 0.90%
			TakerFeeRate:            decimal.NewFromFloat(0.0001), // 0.01%
			MakerFeeRate:            decimal.NewFromFloat(0.0001), // 0.01%
			DirectCommissionRate:    decimal.NewFromFloat(0.70),   // 70% (灵活调整)
			IndirectCommissionRate:  decimal.NewFromFloat(0.09),   // 9% (灵活调整)
			ExtendedCommissionRate:  decimal.NewFromFloat(0.06),   // 6% (灵活调整)
			ActivityCashbackRate:    decimal.NewFromFloat(0.30),   // 30% (灵活调整)
		},
	}

	ctx := context.Background()

	for _, level := range levels {
		if err := upsertAgentLevel(ctx, level); err != nil {
			return fmt.Errorf("failed to upsert level %s: %w", level.Name, err)
		}
		log.Printf("Successfully upserted level: %s", level.Name)
	}

	return nil
}

func upsertAgentLevel(ctx context.Context, level model.AgentLevel) error {
	var existingLevel model.AgentLevel

	err := global.GVA_DB.WithContext(ctx).Where("id = ?", level.ID).First(&existingLevel).Error
	if err != nil {
		if err := global.GVA_DB.WithContext(ctx).Create(&level).Error; err != nil {
			return fmt.Errorf("failed to create level: %w", err)
		}
		return nil
	}

	if err := global.GVA_DB.WithContext(ctx).Save(&level).Error; err != nil {
		return fmt.Errorf("failed to update level: %w", err)
	}

	return nil
}
