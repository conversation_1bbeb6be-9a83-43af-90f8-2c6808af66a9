package initializer

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	repoRebate "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/rebate"
	task2 "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task/level"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task/rebate"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"

	"go.uber.org/zap"
)

func InitTask() {
	scheduler := task2.NewTaskScheduler()
	ctx, cancel := context.WithCancel(context.Background())
	scheduler.SetCancelFunc(cancel)
	go scheduler.RunWithSignal(ctx)

	rebateTask := rebate.NewSnapshotTask(&repoRebate.InvitationRepository{})
	err := scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskAgentReferralSnapshot].ID, global.GVA_CONFIG.CronTasks[utils.TaskAgentReferralSnapshot].Cron, rebateTask.UpdateAllReferralSnapshots)
	if err != nil {
		global.GVA_LOG.Error("task  = ", zap.Any("err", err))
	}

	levelUpgradeTask := level.NewLevelUpgradeTask()
	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskLevelUpgrade].ID, global.GVA_CONFIG.CronTasks[utils.TaskLevelUpgrade].Cron, levelUpgradeTask.UpgradeUserLevels)
	if err != nil {
		global.GVA_LOG.Error("level upgrade task registration failed", zap.Any("err", err))
	}
}
