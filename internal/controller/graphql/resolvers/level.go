package resolvers

import (
	"context"
	"fmt"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/level"
)

type LevelResolver struct {
	s service.LevelI
}

func NewLevelResolver() *LevelResolver {
	return &LevelResolver{
		s: level.NewLevelService(),
	}
}

// AgentLevels is the resolver for the agentLevels field.
func (l *LevelResolver) AgentLevels(ctx context.Context) ([]*gql_model.AgentLevel, error) {
	levels, err := l.s.GetAgentLevels(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get agent levels: %w", err)
	}

	var gqlLevels []*gql_model.AgentLevel
	for _, level := range levels {
		gqlLevel := ModelAgentLevelToGQL(&level)
		gqlLevels = append(gqlLevels, gqlLevel)
	}

	return gqlLevels, nil
}

// AgentLevel is the resolver for the agentLevel field.
func (l *LevelResolver) AgentLevel(ctx context.Context, id int) (*gql_model.AgentLevel, error) {
	level, err := l.s.GetAgentLevelByID(ctx, uint(id))
	if err != nil {
		return nil, fmt.Errorf("failed to get agent level: %w", err)
	}

	return ModelAgentLevelToGQL(level), nil
}

// UpdateLevelCommission is the resolver for the updateLevelCommission field.
func (l *LevelResolver) UpdateLevelCommission(ctx context.Context, input gql_model.UpdateLevelCommissionInput) (*gql_model.UpdateLevelCommissionResponse, error) {
	// Validate levelId - only accept 6 and 7
	var levelID uint
	switch input.LevelID {
	case "6":
		levelID = 6
	case "7":
		levelID = 7
	default:
		return &gql_model.UpdateLevelCommissionResponse{
			Success: false,
			Message: "Invalid levelId: only values 6 and 7 are allowed",
			Level:   nil,
		}, nil
	}

	level, err := l.s.UpdateLevelCommission(ctx, levelID, input.DirectCommissionRate, input.IndirectCommissionRate, input.ExtendedCommissionRate, input.MemeFeeRebate)
	if err != nil {
		return &gql_model.UpdateLevelCommissionResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to update level commission: %v", err),
			Level:   nil,
		}, nil
	}

	return &gql_model.UpdateLevelCommissionResponse{
		Success: true,
		Message: "Level commission updated successfully",
		Level:   ModelAgentLevelToGQL(level),
	}, nil
}

// ModelAgentLevelToGQL converts model.AgentLevel to gql_model.AgentLevel
func ModelAgentLevelToGQL(level *model.AgentLevel) *gql_model.AgentLevel {
	if level == nil {
		return nil
	}

	memeVolumeThreshold, _ := level.MemeVolumeThreshold.Float64()
	contractVolumeThreshold, _ := level.ContractVolumeThreshold.Float64()
	memeFeeRate, _ := level.MemeFeeRate.Float64()
	takerFeeRate, _ := level.TakerFeeRate.Float64()
	makerFeeRate, _ := level.MakerFeeRate.Float64()
	directCommissionRate, _ := level.DirectCommissionRate.Float64()
	indirectCommissionRate, _ := level.IndirectCommissionRate.Float64()
	extendedCommissionRate, _ := level.ExtendedCommissionRate.Float64()
	activityCashbackRate, _ := level.ActivityCashbackRate.Float64()

	return &gql_model.AgentLevel{
		ID:                      int(level.ID),
		Name:                    level.Name,
		MemeVolumeThreshold:     memeVolumeThreshold,
		ContractVolumeThreshold: contractVolumeThreshold,
		MemeFeeRate:             memeFeeRate,
		TakerFeeRate:            takerFeeRate,
		MakerFeeRate:            makerFeeRate,
		DirectCommissionRate:    directCommissionRate,
		IndirectCommissionRate:  indirectCommissionRate,
		ExtendedCommissionRate:  extendedCommissionRate,
		ActivityCashbackRate:    activityCashbackRate,
	}
}
