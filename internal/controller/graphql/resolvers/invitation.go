package resolvers

import (
	"context"
	"fmt"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/rebate"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
)

type InvitationResolver struct {
	s service.InvitationI
	v *validator.Validate
}

func NewInvitationResolver() *InvitationResolver {
	return &InvitationResolver{
		s: rebate.NewInvitationService(),
		v: validator.New(),
	}
}

func (i *InvitationResolver) CreateUserWithReferral(ctx context.Context, input gql_model.CreateUserWithReferralInput) (*gql_model.CreateUserResponse, error) {
	// Extract user ID from JWT token instead of accepting it as input
	userId := GetUserIDFromContext(ctx)
	if userId == uuid.Nil {
		return nil, fmt.Errorf("User ID not found in JWT token")
	}

	referrer, err := i.s.GetUserByInvitationCode(ctx, input.InvitationCode)
	if err != nil || referrer == nil {
		return nil, fmt.Errorf("Invalid invitation code")
	}

	err = i.s.CreateUserWithReferral(ctx, referrer.ID, userId.String())
	if err != nil {
		return nil, fmt.Errorf("Failed to create user association: %v", err)
	}

	// Fetch the user to return in the response (required by schema)
	user, err := i.s.GetUserByID(ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("Failed to fetch user after creating referral: %v", err)
	}

	return &gql_model.CreateUserResponse{
		User:    utils.Translate[gql_model.User](user),
		Success: true,
		Message: "User created and bound referral relationship successfully",
	}, nil
}

// CreateUserInvitationCode is the resolver for the createUserInvitationCode field.
func (i *InvitationResolver) CreateUserInvitationCode(ctx context.Context, input gql_model.CreateUserInvitationCodeInput) (*gql_model.CreateUserResponse, error) {
	userId := GetUserIDFromContext(ctx)
	var walletID, walletAccountID *uuid.UUID
	if input.WalletID != nil {
		id, err := uuid.Parse(*input.WalletID)
		if err != nil {
			return nil, fmt.Errorf("Invalid wallet ID: %w", err)
		}
		walletID = &id
	}

	if input.WalletAccountID != nil {
		id, err := uuid.Parse(*input.WalletAccountID)
		if err != nil {
			return nil, fmt.Errorf("Invalid wallet account ID: %w", err)
		}
		walletAccountID = &id
	}

	name := ""
	if input.Name != nil {
		name = *input.Name
	}
	chain := ""
	if input.Chain != nil {
		chain = *input.Chain
	}
	walletAddress := ""
	if input.WalletAddress != nil {
		walletAddress = *input.WalletAddress
	}
	email := ""
	if input.Email != nil {
		email = *input.Email
	}

	walletType := GQLWalletTypeToString(input.WalletType)

	user, err := i.s.UpdateUserInvitationCode(ctx, userId,
		chain, name, walletAddress, walletID, walletAccountID,
		walletType, input.InvitationCode, email)
	if err != nil {
		return nil, fmt.Errorf("failed to update user invitation code: %w", err)
	}

	return &gql_model.CreateUserResponse{
		User:    utils.Translate[gql_model.User](user),
		Success: true,
		Message: "User created successfully",
	}, nil
}

// User is the resolver for the user field.
func (i *InvitationResolver) User(ctx context.Context) (*gql_model.User, error) {
	userId := GetUserIDFromContext(ctx)
	user, err := i.s.GetUserByID(ctx, userId)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return utils.Translate[gql_model.User](user), nil
}

// ReferralSnapshot is the resolver for the referralSnapshot field.
func (i *InvitationResolver) ReferralSnapshot(ctx context.Context) (*gql_model.ReferralSnapshot, error) {
	userId := GetUserIDFromContext(ctx)
	snapshot, err := i.s.GetReferralSnapshot(ctx, userId)
	if err != nil {
		return ModelReferralSnapshotToGQL(&model.ReferralSnapshot{}), nil
	}

	return ModelReferralSnapshotToGQL(snapshot), nil
}

func GetUserIDFromContext(ctx context.Context) uuid.UUID {
	userIdStr, _ := ctx.Value("userId").(string)
	userId := uuid.Nil
	if userIdStr != "" {
		userId, _ = uuid.Parse(userIdStr)
	}
	return userId
}

func GetUserEmailFromContext(ctx context.Context) string {
	email, _ := ctx.Value("userEmail").(string)
	return email
}

func GQLWalletTypeToString(walletType gql_model.WalletType) string {
	switch walletType {
	case gql_model.WalletTypeEmbedded:
		return "embedded"
	case gql_model.WalletTypeManaged:
		return "managed"
	default:
		return ""
	}
}

func ModelReferralSnapshotToGQL(snapshot *model.ReferralSnapshot) *gql_model.ReferralSnapshot {
	if snapshot == nil {
		return nil
	}

	totalVolumeUsd, _ := snapshot.TotalVolumeUSD.Float64()
	totalRewardsDistributed, _ := snapshot.TotalRewardsDistributed.Float64()

	return &gql_model.ReferralSnapshot{
		UserID:                  snapshot.UserID.String(),
		DirectCount:             snapshot.DirectCount,
		TotalDownlineCount:      snapshot.TotalDownlineCount,
		TotalVolumeUsd:          totalVolumeUsd,
		TotalRewardsDistributed: totalRewardsDistributed,
		User:                    utils.Translate[gql_model.User](&snapshot.User),
	}
}

// UserInvitationData is the resolver for the userInvitationData field.
func (i *InvitationResolver) UserInvitationData(ctx context.Context) (*gql_model.UserInvitationDataResponse, error) {
	userId := GetUserIDFromContext(ctx)
	if userId == uuid.Nil {
		return &gql_model.UserInvitationDataResponse{
			Success: false,
			Message: "User is not logged in",
		}, nil
	}

	invitationData := &gql_model.UserInvitationData{
		InvitedUserCount:          0,
		TradingUserCount:          0,
		IndirectInvitedUserCount:  0,
		ExtendedInvitedUserCount:  0,
		InvitedAddresses:          []string{},
		MemeTransactionVolume:     0,
		ContractTransactionVolume: 0,
		TotalTransactionVolume:    0,
	}

	snapshot, err := i.s.GetReferralSnapshot(ctx, userId)
	if err == nil && snapshot != nil {
		invitationData.InvitedUserCount = snapshot.DirectCount

		indirectInvitedCount := snapshot.TotalDownlineCount - snapshot.DirectCount
		if indirectInvitedCount > 0 {
			invitationData.IndirectInvitedUserCount = indirectInvitedCount
		}

		if totalVolumeUsd, ok := snapshot.TotalVolumeUSD.Float64(); ok {
			invitationData.TotalTransactionVolume = totalVolumeUsd
		}
	}

	if tradingUserCount, err := i.s.GetTradingUserCount(ctx, userId, nil, nil); err == nil {
		invitationData.TradingUserCount = tradingUserCount
	}

	if extendedInvitedCount, err := i.s.GetExtendedInvitedUserCount(ctx, userId); err == nil {
		invitationData.ExtendedInvitedUserCount = extendedInvitedCount
	}

	if invitedAddresses, err := i.s.GetInvitedAddresses(ctx, userId); err == nil {
		invitationData.InvitedAddresses = invitedAddresses
	}

	if activityTransactionVolume, err := i.s.GetActivityTransactionVolume(ctx, userId, nil, nil); err == nil {
		invitationData.MemeTransactionVolume = activityTransactionVolume
	}

	if contractTransactionVolume, err := i.s.GetContractTransactionVolume(ctx, userId, nil, nil); err == nil {
		invitationData.ContractTransactionVolume = contractTransactionVolume
	}

	return &gql_model.UserInvitationDataResponse{
		Success: true,
		Message: "Successfully obtained user invitation data",
		Data:    invitationData,
	}, nil
}
