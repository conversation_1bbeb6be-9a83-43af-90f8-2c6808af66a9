#type ReferralSnapshot {
#  userId: ID!
#  directCount: Int!
#  totalDownlineCount: Int!
#  totalVolumeUsd: Float!
#  totalRewardsDistributed: Float!
#  l1UplineId: ID
#  l2UplineId: ID
#  l3UplineId: ID
#  user: User!
#  l1Upline: User
#  l2Upline: User
#  l3Upline: User
#}

type ReferralSnapshot {
  userId: ID!
  directCount: Int!
  totalDownlineCount: Int!
  totalVolumeUsd: Float!
  totalRewardsDistributed: Float!
  user: User!
}

enum WalletType {
  EMBEDDED
  MANAGED
}

input CreateUserInput {
  email: String!
  invitationCode: String
  referrerCode: String
}

input CreateUserInvitationCodeInput {
  invitationCode: String!
  email: String
  chain: String
  name: String
  walletAddress: String
  walletId: ID
  walletAccountId: ID
  walletType: WalletType!
}

type CreateUserResponse {
  user: User!
  success: Boolean!
  message: String!
}

input CreateUserWithReferralInput {
  invitationCode: String!
}

type User {
  id: ID!
  email: String
  invitationCode: String
  createdAt: String!
  updatedAt: String!
  deletedAt: String
  agentLevelId: Int!
  agentLevel: AgentLevel!
  levelGracePeriodStartedAt: String
  levelUpgradedAt: String
  firstTransactionAt: String
  referrals: [Referral!]!
  referralSnapshot: ReferralSnapshot
  referredUsers: [Referral!]!
}

type AgentLevel {
  id: Int!
  name: String!
  memeVolumeThreshold: Float!
  contractVolumeThreshold: Float!
  memeFeeRate: Float!
  takerFeeRate: Float!
  makerFeeRate: Float!
  directCommissionRate: Float!
  indirectCommissionRate: Float!
  extendedCommissionRate: Float!
  activityCashbackRate: Float!
}

type Referral {
  id: Int!
  userId: ID!
  referrerId: ID
  depth: Int!
  createdAt: String!
  user: User!
  referrer: User
}

type UserInvitationData {
  invitedUserCount: Int!
  tradingUserCount: Int!
  indirectInvitedUserCount: Int!
  extendedInvitedUserCount: Int!
  invitedAddresses: [String!]!
  memeTransactionVolume: Float!
  contractTransactionVolume: Float!
  totalTransactionVolume: Float!
}

type UserInvitationDataResponse {
  success: Boolean!
  message: String!
  data: UserInvitationData
}
