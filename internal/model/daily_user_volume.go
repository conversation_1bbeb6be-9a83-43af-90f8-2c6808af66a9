package model

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

type DailyUserVolume struct {
	ID                uuid.UUID       `json:"id" gorm:"column:id;type:uuid;primaryKey;default:gen_random_uuid()"`
	UserID            uuid.UUID       `gorm:"type:uuid;not null;index:idx_user_date,unique" json:"user_id"`
	Date              time.Time       `gorm:"type:date;not null;index:idx_user_date,unique" json:"date"`
	MemeVolumeUSD     decimal.Decimal `gorm:"type:numeric(38,2);default:0" json:"meme_volume_usd"`
	ContractVolumeUSD decimal.Decimal `gorm:"type:numeric(38,2);default:0" json:"contract_volume_usd"`
}

func (DailyUserVolume) TableName() string {
	return "daily_user_volumes"
}