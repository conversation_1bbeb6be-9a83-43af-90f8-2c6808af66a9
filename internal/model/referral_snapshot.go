package model

import (
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
)

// ReferralSnapshot represents the referral_snapshots table
type ReferralSnapshot struct {
	UserID                  uuid.UUID       `gorm:"type:uuid;primary_key" json:"user_id"`
	DirectCount             int             `gorm:"default:0" json:"direct_count"`
	TotalDownlineCount      int             `gorm:"default:0" json:"total_downline_count"`
	TotalVolumeUSD          decimal.Decimal `gorm:"type:numeric(38,2);default:0" json:"total_volume_usd"`
	TotalRewardsDistributed decimal.Decimal `gorm:"type:numeric(38,6);default:0" json:"total_rewards_distributed"`

	L1UplineID *uuid.UUID `gorm:"type:uuid;index" json:"l1_upline_id"`
	L2UplineID *uuid.UUID `gorm:"type:uuid;index" json:"l2_upline_id"`
	L3UplineID *uuid.UUID `gorm:"type:uuid;index" json:"l3_upline_id"`

	// Relationships
	User     User  `gorm:"foreignKey:UserID;references:ID;constraint:false" json:"user"`
	L1Upline *User `gorm:"foreignKey:L1UplineID" json:"l1_upline,omitempty"`
	L2Upline *User `gorm:"foreignKey:L2UplineID" json:"l2_upline,omitempty"`
	L3Upline *User `gorm:"foreignKey:L3UplineID" json:"l3_upline,omitempty"`
}

// TableName specifies the table name for ReferralSnapshot
func (ReferralSnapshot) TableName() string {
	return "referral_snapshots"
}
