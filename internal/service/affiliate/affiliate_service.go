package affiliate

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	natsModel "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
)

// AffiliateService handles business logic for affiliate transactions
type AffiliateService struct {
	affiliateRepo           repo.AffiliateRepositoryInterface
	userRepo                transaction.UserRepositoryInterface
	activityCashbackService *ActivityCashbackService

	// SOL price tracking (thread-safe)
	priceMutex         sync.RWMutex
	latestSolPrice     decimal.Decimal
	latestSolPriceTime time.Time
}

// NewAffiliateService creates a new affiliate service
func NewAffiliateService() AffiliateServiceInterface {
	return &AffiliateService{
		affiliateRepo:           repo.NewAffiliateRepository(),
		userRepo:                transaction.NewUserRepository(),
		activityCashbackService: NewActivityCashbackService(),
	}
}

// ProcessAffiliateTransaction processes an affiliate transaction event from NATS
func (s *AffiliateService) ProcessAffiliateTransaction(ctx context.Context, txEvent *natsModel.AffiliateTxEvent) error {
	global.GVA_LOG.Info("Processing affiliate transaction",
		zap.String("order_id", txEvent.ID.String()),
		zap.String("user_id", txEvent.UserId),
		zap.String("transaction_type", string(txEvent.TransactionType)),
		zap.String("status", string(txEvent.Status)),
		zap.String("raw_txid", txEvent.Txid),     // Log raw txid from NATS
		zap.Bool("has_txid", txEvent.Txid != "")) // Log whether txid exists

	// Check if transaction already exists
	existingTx, err := s.affiliateRepo.GetAffiliateTransactionByOrderID(ctx, txEvent.ID)
	if err != nil && err != gorm.ErrRecordNotFound {
		return fmt.Errorf("failed to check existing transaction: %w", err)
	}

	// If transaction exists, update it
	if existingTx != nil {
		return s.updateExistingTransaction(ctx, existingTx, txEvent)
	}

	// Create new transaction
	return s.createNewTransaction(ctx, txEvent)
}

// ProcessSolPriceUpdate processes a SOL price update event from NATS
// Note: This method now only stores the price in memory for later use when affiliate transactions occur
func (s *AffiliateService) ProcessSolPriceUpdate(ctx context.Context, priceEvent *natsModel.SolPriceEvent) error {
	global.GVA_LOG.Debug("Processing SOL price update",
		zap.String("symbol", priceEvent.GetSymbol()),
		zap.String("price", priceEvent.UsdPrice.String()),
		zap.Time("timestamp", priceEvent.GetTime()))

	// Store the latest price in memory for use when affiliate transactions occur
	// We don't save to database here anymore - only when affiliate transactions happen
	s.priceMutex.Lock()
	s.latestSolPrice = priceEvent.UsdPrice
	s.latestSolPriceTime = priceEvent.GetTime()
	s.priceMutex.Unlock()

	global.GVA_LOG.Debug("SOL price updated in memory",
		zap.String("symbol", priceEvent.GetSymbol()),
		zap.String("price", priceEvent.UsdPrice.String()),
		zap.Time("timestamp", priceEvent.GetTime()))

	return nil
}

// createNewTransaction creates a new affiliate transaction
func (s *AffiliateService) createNewTransaction(ctx context.Context, txEvent *natsModel.AffiliateTxEvent) error {
	// Validate user ID
	if txEvent.UserId == "" {
		return fmt.Errorf("user ID is empty")
	}

	// Parse user ID
	userID, err := uuid.Parse(txEvent.UserId)
	if err != nil {
		return fmt.Errorf("invalid user ID: %w", err)
	}

	// Get referral information
	var referrerID *uuid.UUID
	var referralDepth int
	var commissionRate decimal.Decimal
	var commissionAmount decimal.Decimal

	// Try to get referral info directly (this will also validate user exists)
	referralInfo, err := s.userRepo.GetReferralInfo(ctx, userID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			global.GVA_LOG.Debug("No referral info found for user, creating transaction without referral",
				zap.String("user_id", txEvent.UserId))
		} else {
			global.GVA_LOG.Warn("Failed to get referral info, creating transaction without referral",
				zap.String("user_id", txEvent.UserId),
				zap.Error(err))
		}
	} else if referralInfo != nil && referralInfo.ReferrerID != nil {
		referrerID = referralInfo.ReferrerID
		referralDepth = referralInfo.Depth

		// Calculate commission (example: 0.1% for direct referrals)
		commissionRate = decimal.NewFromFloat(0.001) // 0.1%
		commissionAmount = txEvent.QuoteAmount.Mul(commissionRate)

		global.GVA_LOG.Debug("Found referral info for transaction",
			zap.String("user_id", txEvent.UserId),
			zap.String("referrer_id", referrerID.String()),
			zap.Int("depth", referralDepth),
			zap.String("commission_amount", commissionAmount.String()))
	}

	// Convert NATS model types to database model types
	transactionType := model.TransactionType(txEvent.TransactionType)
	orderType := model.OrderType(txEvent.Type)
	status := model.TransactionStatus(txEvent.Status)

	// Calculate volume in USD (assuming quote amount is in USD for now)
	volumeUSD := txEvent.QuoteAmount

	// Handle TxHash - use order_id if txHash is empty to avoid duplicate key errors
	txHash := s.getTxHashWithLogging(txEvent)

	// Create affiliate transaction
	affiliateTx := &model.AffiliateTransaction{
		OrderID:          txEvent.ID,
		CreatedAt:        txEvent.CreatedAt,
		TransactionType:  transactionType,
		Type:             orderType,
		ChainID:          txEvent.ChainId,
		BaseAddress:      txEvent.BaseAddress,
		BaseSymbol:       txEvent.BaseSymbol,
		QuoteAddress:     txEvent.QuoteAddress,
		QuoteSymbol:      txEvent.QuoteSymbol,
		UserID:           userID,
		UserAddress:      txEvent.UserAddress,
		BaseAmount:       txEvent.BaseAmount,
		QuoteAmount:      txEvent.QuoteAmount,
		TotalFee:         txEvent.TotalFee,
		Slippage:         txEvent.Slippage,
		Status:           status,
		TxHash:           txHash, // Use processed txHash
		MevProtect:       txEvent.MevProtect,
		ReferrerID:       referrerID,
		ReferralDepth:    referralDepth,
		CommissionRate:   commissionRate,
		CommissionAmount: commissionAmount,
		CommissionPaid:   false,
		VolumeUSD:        volumeUSD,
	}

	if err := s.affiliateRepo.CreateAffiliateTransaction(ctx, affiliateTx); err != nil {
		// Check if it's a duplicate key error
		if strings.Contains(err.Error(), "duplicate key value violates unique constraint") {
			global.GVA_LOG.Warn("Duplicate transaction detected, skipping",
				zap.String("order_id", txEvent.ID.String()),
				zap.String("tx_hash", txHash),
				zap.Error(err))
			return nil // Skip duplicate transactions gracefully
		}
		return fmt.Errorf("failed to create affiliate transaction: %w", err)
	}

	// Save SOL price snapshot when affiliate transaction is created
	if err := s.saveSolPriceSnapshot(ctx); err != nil {
		global.GVA_LOG.Warn("Failed to save SOL price snapshot",
			zap.String("order_id", txEvent.ID.String()),
			zap.Error(err))
		// Don't fail the transaction creation if price snapshot fails
	}

	// Process activity transaction cashback if transaction is completed
	if affiliateTx.Status == model.StatusCompleted {
		if err := s.activityCashbackService.ProcessActivityTransactionCashback(ctx, affiliateTx); err != nil {
			global.GVA_LOG.Error("Failed to process activity transaction cashback",
				zap.String("order_id", txEvent.ID.String()),
				zap.Error(err))
			// Don't fail the transaction creation if cashback processing fails
		}
	}

	global.GVA_LOG.Info("Affiliate transaction created successfully",
		zap.String("order_id", txEvent.ID.String()),
		zap.Uint("transaction_id", affiliateTx.ID),
		zap.String("commission_amount", commissionAmount.String()))

	return nil
}

// updateExistingTransaction updates an existing affiliate transaction
func (s *AffiliateService) updateExistingTransaction(ctx context.Context, existingTx *model.AffiliateTransaction, txEvent *natsModel.AffiliateTxEvent) error {
	// Update fields that might change
	existingTx.Status = model.TransactionStatus(txEvent.Status)

	// Handle TxHash - use order_id if txHash is empty to avoid duplicate key errors
	// Only update TxHash if the new value is not empty, to preserve existing fallback values
	if txEvent.Txid != "" {
		// Check if we're overwriting a fallback value (order_id) with a real tx_hash
		if existingTx.TxHash == txEvent.ID.String() {
			global.GVA_LOG.Info("Updating tx_hash from order_id fallback to actual tx_hash",
				zap.String("order_id", txEvent.ID.String()),
				zap.String("old_tx_hash", existingTx.TxHash),
				zap.String("new_tx_hash", txEvent.Txid))
		}
		existingTx.TxHash = txEvent.Txid
	} else if existingTx.TxHash == "" {
		// If both current and new TxHash are empty, use order_id as fallback
		existingTx.TxHash = txEvent.ID.String()
		global.GVA_LOG.Warn("Using order_id as tx_hash fallback during update",
			zap.String("order_id", txEvent.ID.String()),
			zap.String("status", string(txEvent.Status)),
			zap.String("reason", "both_current_and_new_txid_empty"))
	} else {
		// Preserve existing tx_hash if new one is empty (don't overwrite real tx_hash with order_id)
		global.GVA_LOG.Debug("Preserving existing tx_hash, new txid is empty",
			zap.String("order_id", txEvent.ID.String()),
			zap.String("existing_tx_hash", existingTx.TxHash),
			zap.String("status", string(txEvent.Status)))
	}

	existingTx.BaseAmount = txEvent.BaseAmount
	existingTx.QuoteAmount = txEvent.QuoteAmount
	existingTx.TotalFee = txEvent.TotalFee
	existingTx.VolumeUSD = txEvent.QuoteAmount // Update volume

	// Recalculate commission if transaction is completed and commission hasn't been paid
	if existingTx.Status == model.StatusCompleted && !existingTx.CommissionPaid && existingTx.ReferrerID != nil {
		existingTx.CommissionAmount = existingTx.VolumeUSD.Mul(existingTx.CommissionRate)
	}

	if err := s.affiliateRepo.UpdateAffiliateTransaction(ctx, existingTx); err != nil {
		// Check if it's a duplicate key error
		if strings.Contains(err.Error(), "duplicate key value violates unique constraint") {
			global.GVA_LOG.Warn("Duplicate transaction detected during update, skipping",
				zap.String("order_id", txEvent.ID.String()),
				zap.String("tx_hash", existingTx.TxHash),
				zap.Error(err))
			return nil // Skip duplicate transactions gracefully
		}
		return fmt.Errorf("failed to update affiliate transaction: %w", err)
	}

	// Process activity transaction cashback if transaction status changed to completed
	if existingTx.Status == model.StatusCompleted {
		if err := s.activityCashbackService.ProcessActivityTransactionCashback(ctx, existingTx); err != nil {
			global.GVA_LOG.Error("Failed to process activity transaction cashback",
				zap.String("order_id", txEvent.ID.String()),
				zap.Error(err))
			// Don't fail the transaction update if cashback processing fails
		}
	}

	// Save SOL price snapshot when affiliate transaction is updated
	if err := s.saveSolPriceSnapshot(ctx); err != nil {
		global.GVA_LOG.Warn("Failed to save SOL price snapshot",
			zap.String("order_id", txEvent.ID.String()),
			zap.Error(err))
		// Don't fail the transaction update if price snapshot fails
	}

	global.GVA_LOG.Info("Affiliate transaction updated successfully",
		zap.String("order_id", txEvent.ID.String()),
		zap.Uint("transaction_id", existingTx.ID),
		zap.String("status", string(existingTx.Status)))

	return nil
}

// isValidTxHash checks if a tx_hash is a real transaction hash or just an order_id fallback
func (s *AffiliateService) isValidTxHash(txHash string, orderID uuid.UUID) bool {
	// If tx_hash equals order_id string, it's a fallback value, not a real tx_hash
	return txHash != orderID.String()
}

// saveSolPriceSnapshot saves the current SOL price to database when affiliate transactions occur
// This ensures we only store price data when there's actual trading activity
func (s *AffiliateService) saveSolPriceSnapshot(ctx context.Context) error {
	s.priceMutex.RLock()
	price := s.latestSolPrice
	timestamp := s.latestSolPriceTime
	s.priceMutex.RUnlock()

	// Check if we have valid price data
	if price.IsZero() || timestamp.IsZero() {
		return fmt.Errorf("no valid SOL price data available")
	}

	// Create price snapshot
	snapshot := &model.SolPriceSnapshot{
		Price:     price,
		Timestamp: timestamp,
	}

	// Use upsert to ensure only one record per timestamp (millisecond precision)
	if err := s.affiliateRepo.UpsertSolPriceSnapshot(ctx, snapshot); err != nil {
		return fmt.Errorf("failed to upsert SOL price snapshot: %w", err)
	}

	global.GVA_LOG.Debug("SOL price snapshot saved to database",
		zap.String("price", price.String()),
		zap.Time("timestamp", timestamp))

	return nil
}

// getTxHashWithLogging returns the appropriate tx_hash with detailed logging
func (s *AffiliateService) getTxHashWithLogging(txEvent *natsModel.AffiliateTxEvent) string {
	if txEvent.Txid != "" {
		global.GVA_LOG.Debug("Using actual tx_hash from NATS",
			zap.String("order_id", txEvent.ID.String()),
			zap.String("tx_hash", txEvent.Txid),
			zap.String("status", string(txEvent.Status)))
		return txEvent.Txid
	}

	// Use order_id as fallback
	fallbackHash := txEvent.ID.String()
	global.GVA_LOG.Warn("Using order_id as tx_hash fallback",
		zap.String("order_id", txEvent.ID.String()),
		zap.String("status", string(txEvent.Status)),
		zap.String("transaction_type", string(txEvent.TransactionType)),
		zap.String("reason", "empty_txid_from_nats"))

	return fallbackHash
}

// GetUserTransactions retrieves affiliate transactions for a user
func (s *AffiliateService) GetUserTransactions(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.AffiliateTransaction, error) {
	return s.affiliateRepo.GetAffiliateTransactionsByUserID(ctx, userID, limit, offset)
}

// GetReferrerTransactions retrieves affiliate transactions for a referrer
func (s *AffiliateService) GetReferrerTransactions(ctx context.Context, referrerID uuid.UUID, limit, offset int) ([]model.AffiliateTransaction, error) {
	return s.affiliateRepo.GetAffiliateTransactionsByReferrerID(ctx, referrerID, limit, offset)
}

// GetUnpaidCommissions retrieves unpaid commissions for a referrer
func (s *AffiliateService) GetUnpaidCommissions(ctx context.Context, referrerID uuid.UUID) ([]model.AffiliateTransaction, error) {
	return s.affiliateRepo.GetUnpaidCommissions(ctx, referrerID)
}

// MarkCommissionsAsPaid marks commissions as paid
func (s *AffiliateService) MarkCommissionsAsPaid(ctx context.Context, transactionIDs []uint) error {
	return s.affiliateRepo.MarkCommissionAsPaid(ctx, transactionIDs)
}

// GetUserStats retrieves statistics for a user
func (s *AffiliateService) GetUserStats(ctx context.Context, userID uuid.UUID) (*UserStats, error) {
	totalVolume, err := s.affiliateRepo.GetUserTotalVolume(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user total volume: %w", err)
	}

	return &UserStats{
		UserID:      userID,
		TotalVolume: totalVolume,
	}, nil
}

// GetReferrerStats retrieves statistics for a referrer
func (s *AffiliateService) GetReferrerStats(ctx context.Context, referrerID uuid.UUID) (*ReferrerStats, error) {
	totalCommission, err := s.affiliateRepo.GetReferrerTotalCommission(ctx, referrerID)
	if err != nil {
		return nil, fmt.Errorf("failed to get referrer total commission: %w", err)
	}

	unpaidCommission, err := s.affiliateRepo.GetReferrerUnpaidCommission(ctx, referrerID)
	if err != nil {
		return nil, fmt.Errorf("failed to get referrer unpaid commission: %w", err)
	}

	return &ReferrerStats{
		ReferrerID:       referrerID,
		TotalCommission:  totalCommission,
		UnpaidCommission: unpaidCommission,
		PaidCommission:   totalCommission.Sub(unpaidCommission),
	}, nil
}

// UserStats represents user statistics
type UserStats struct {
	UserID      uuid.UUID       `json:"user_id"`
	TotalVolume decimal.Decimal `json:"total_volume"`
}

// ReferrerStats represents referrer statistics
type ReferrerStats struct {
	ReferrerID       uuid.UUID       `json:"referrer_id"`
	TotalCommission  decimal.Decimal `json:"total_commission"`
	UnpaidCommission decimal.Decimal `json:"unpaid_commission"`
	PaidCommission   decimal.Decimal `json:"paid_commission"`
}
