package service

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/test"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
)

// MockInvitationRepo is a mock implementation of repo.InvitationRepo
type MockInvitationRepo struct {
	mock.Mock
}

func (m *MockInvitationRepo) GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*model.ReferralSnapshot), args.Error(1)
}

func (m *MockInvitationRepo) UpdateUserInvitationCode(ctx context.Context, userID uuid.UUID, chain, name, walletAddress string, walletID, walletAccountID *uuid.UUID, walletType, invitationCode, email string) (*model.User, error) {
	args := m.Called(ctx, userID, chain, name, walletAddress, walletID, walletAccountID, walletType, invitationCode, email)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockInvitationRepo) CreateUserWithReferral(ctx context.Context, referrerID uuid.UUID, userID string) error {
	args := m.Called(ctx, referrerID, userID)
	return args.Error(0)
}

func (m *MockInvitationRepo) GetUserByInvitationCode(ctx context.Context, invitationCode string) (*model.User, error) {
	args := m.Called(ctx, invitationCode)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockInvitationRepo) GetUserByID(ctx context.Context, id uuid.UUID) (*model.User, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockInvitationRepo) GetTradingUserCount(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (int, error) {
	args := m.Called(ctx, userID, startTime, endTime)
	return args.Int(0), args.Error(1)
}

func (m *MockInvitationRepo) GetExtendedInvitedUserCount(ctx context.Context, userID uuid.UUID) (int, error) {
	args := m.Called(ctx, userID)
	return args.Int(0), args.Error(1)
}

func (m *MockInvitationRepo) GetInvitedAddresses(ctx context.Context, userID uuid.UUID) ([]string, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]string), args.Error(1)
}

func (m *MockInvitationRepo) GetActivityTransactionVolume(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (float64, error) {
	args := m.Called(ctx, userID, startTime, endTime)
	return args.Get(0).(float64), args.Error(1)
}

func (m *MockInvitationRepo) GetContractTransactionVolume(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (float64, error) {
	args := m.Called(ctx, userID, startTime, endTime)
	return args.Get(0).(float64), args.Error(1)
}

// Mock service implementation for testing
type MockInvitationService struct {
	invitationRepo InvitationRepo
}

func NewMockInvitationService(repo InvitationRepo) InvitationI {
	return &MockInvitationService{
		invitationRepo: repo,
	}
}

func (s *MockInvitationService) GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error) {
	return s.invitationRepo.GetReferralSnapshot(ctx, userID)
}

func (s *MockInvitationService) UpdateUserInvitationCode(ctx context.Context, userID uuid.UUID, chain, name, walletAddress string, walletID, walletAccountID *uuid.UUID, walletType, invitationCode, email string) (*model.User, error) {
	return s.invitationRepo.UpdateUserInvitationCode(ctx, userID, chain, name, walletAddress, walletID, walletAccountID, walletType, invitationCode, email)
}

func (s *MockInvitationService) CreateUserWithReferral(ctx context.Context, referrerID uuid.UUID, userID string) error {
	return s.invitationRepo.CreateUserWithReferral(ctx, referrerID, userID)
}

func (s *MockInvitationService) GetUserByInvitationCode(ctx context.Context, invitationCode string) (*model.User, error) {
	return s.invitationRepo.GetUserByInvitationCode(ctx, invitationCode)
}

func (s *MockInvitationService) GetUserByID(ctx context.Context, id uuid.UUID) (*model.User, error) {
	return s.invitationRepo.GetUserByID(ctx, id)
}

func (s *MockInvitationService) GetTradingUserCount(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (int, error) {
	return s.invitationRepo.GetTradingUserCount(ctx, userID, startTime, endTime)
}

func (s *MockInvitationService) GetExtendedInvitedUserCount(ctx context.Context, userID uuid.UUID) (int, error) {
	return s.invitationRepo.GetExtendedInvitedUserCount(ctx, userID)
}

func (s *MockInvitationService) GetInvitedAddresses(ctx context.Context, userID uuid.UUID) ([]string, error) {
	return s.invitationRepo.GetInvitedAddresses(ctx, userID)
}

func (s *MockInvitationService) GetActivityTransactionVolume(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (float64, error) {
	return s.invitationRepo.GetActivityTransactionVolume(ctx, userID, startTime, endTime)
}

func (s *MockInvitationService) GetContractTransactionVolume(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (float64, error) {
	return s.invitationRepo.GetContractTransactionVolume(ctx, userID, startTime, endTime)
}

// Define the interface for testing
type InvitationRepo interface {
	GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error)
	UpdateUserInvitationCode(ctx context.Context, userID uuid.UUID, chain, name, walletAddress string, walletID, walletAccountID *uuid.UUID, walletType, invitationCode, email string) (*model.User, error)
	CreateUserWithReferral(ctx context.Context, referrerID uuid.UUID, userID string) error
	GetUserByInvitationCode(ctx context.Context, invitationCode string) (*model.User, error)
	GetUserByID(ctx context.Context, id uuid.UUID) (*model.User, error)
	GetTradingUserCount(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (int, error)
	GetExtendedInvitedUserCount(ctx context.Context, userID uuid.UUID) (int, error)
	GetInvitedAddresses(ctx context.Context, userID uuid.UUID) ([]string, error)
	GetActivityTransactionVolume(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (float64, error)
	GetContractTransactionVolume(ctx context.Context, userID uuid.UUID, startTime, endTime *time.Time) (float64, error)
}

func TestInvitationService_GetReferralSnapshot(t *testing.T) {
	// Setup test configuration
	test.SetupTestConfig()
	defer test.CleanupTestConfig()

	fixtures := test.NewTestFixtures()
	helper := test.NewTestHelper(t)

	tests := []struct {
		name             string
		userID           uuid.UUID
		mockSetup        func(*MockInvitationRepo)
		expectedSnapshot *model.ReferralSnapshot
		expectedError    error
	}{
		{
			name:   "successful retrieval of referral snapshot",
			userID: uuid.New(),
			mockSetup: func(mockRepo *MockInvitationRepo) {
				userID := uuid.New()
				snapshot := fixtures.CreateTestReferralSnapshot(userID)
				mockRepo.On("GetReferralSnapshot", mock.Anything, mock.AnythingOfType("uuid.UUID")).Return(snapshot, nil)
			},
			expectedSnapshot: fixtures.CreateTestReferralSnapshot(uuid.New()),
			expectedError:    nil,
		},
		{
			name:   "user not found",
			userID: uuid.New(),
			mockSetup: func(mockRepo *MockInvitationRepo) {
				mockRepo.On("GetReferralSnapshot", mock.Anything, mock.AnythingOfType("uuid.UUID")).Return((*model.ReferralSnapshot)(nil), errors.New("user not found"))
			},
			expectedSnapshot: nil,
			expectedError:    errors.New("user not found"),
		},
		{
			name:   "repository error",
			userID: uuid.New(),
			mockSetup: func(mockRepo *MockInvitationRepo) {
				mockRepo.On("GetReferralSnapshot", mock.Anything, mock.AnythingOfType("uuid.UUID")).Return((*model.ReferralSnapshot)(nil), errors.New("database error"))
			},
			expectedSnapshot: nil,
			expectedError:    errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock repository
			mockRepo := &MockInvitationRepo{}
			tt.mockSetup(mockRepo)

			// Create service with mock repository
			service := NewMockInvitationService(mockRepo)

			// Execute test
			ctx := helper.CreateTestContext()
			result, err := service.GetReferralSnapshot(ctx, tt.userID)

			// Verify results
			if tt.expectedError != nil {
				helper.AssertError(err)
				helper.AssertEqual(tt.expectedError.Error(), err.Error())
				helper.AssertNil(result)
			} else {
				helper.AssertNoError(err)
				helper.AssertNotNil(result)
				if tt.expectedSnapshot != nil {
					helper.AssertEqual(tt.expectedSnapshot.DirectCount, result.DirectCount)
					helper.AssertEqual(tt.expectedSnapshot.TotalDownlineCount, result.TotalDownlineCount)
				}
			}

			// Verify mock expectations
			mockRepo.AssertExpectations(t)
		})
	}
}

func TestInvitationService_GetUserByInvitationCode(t *testing.T) {
	// Setup test configuration
	test.SetupTestConfig()
	defer test.CleanupTestConfig()

	fixtures := test.NewTestFixtures()
	helper := test.NewTestHelper(t)

	tests := []struct {
		name           string
		invitationCode string
		mockSetup      func(*MockInvitationRepo)
		expectedUser   *model.User
		expectedError  error
	}{
		{
			name:           "successful retrieval by invitation code",
			invitationCode: "TEST123",
			mockSetup: func(mockRepo *MockInvitationRepo) {
				user := fixtures.CreateTestUser()
				mockRepo.On("GetUserByInvitationCode", mock.Anything, "TEST123").Return(user, nil)
			},
			expectedUser:  fixtures.CreateTestUser(),
			expectedError: nil,
		},
		{
			name:           "user not found",
			invitationCode: "NOTFOUND",
			mockSetup: func(mockRepo *MockInvitationRepo) {
				mockRepo.On("GetUserByInvitationCode", mock.Anything, "NOTFOUND").Return((*model.User)(nil), errors.New("user not found"))
			},
			expectedUser:  nil,
			expectedError: errors.New("user not found"),
		},
		{
			name:           "empty invitation code",
			invitationCode: "",
			mockSetup: func(mockRepo *MockInvitationRepo) {
				mockRepo.On("GetUserByInvitationCode", mock.Anything, "").Return((*model.User)(nil), errors.New("invalid invitation code"))
			},
			expectedUser:  nil,
			expectedError: errors.New("invalid invitation code"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock repository
			mockRepo := &MockInvitationRepo{}
			tt.mockSetup(mockRepo)

			// Create service with mock repository
			service := NewMockInvitationService(mockRepo)

			// Execute test
			ctx := helper.CreateTestContext()
			result, err := service.GetUserByInvitationCode(ctx, tt.invitationCode)

			// Verify results
			if tt.expectedError != nil {
				helper.AssertError(err)
				helper.AssertEqual(tt.expectedError.Error(), err.Error())
				helper.AssertNil(result)
			} else {
				helper.AssertNoError(err)
				helper.AssertNotNil(result)
				if tt.expectedUser != nil {
					helper.AssertEqual(tt.expectedUser.AgentLevelID, result.AgentLevelID)
					helper.AssertNotNil(result.InvitationCode)
				}
			}

			// Verify mock expectations
			mockRepo.AssertExpectations(t)
		})
	}
}

func TestInvitationService_CreateUserWithReferral(t *testing.T) {
	// Setup test configuration
	test.SetupTestConfig()
	defer test.CleanupTestConfig()

	helper := test.NewTestHelper(t)

	tests := []struct {
		name          string
		referrerID    uuid.UUID
		userID        string
		mockSetup     func(*MockInvitationRepo)
		expectedError error
	}{
		{
			name:       "successful user creation with referral",
			referrerID: uuid.New(),
			userID:     "new-user-123",
			mockSetup: func(mockRepo *MockInvitationRepo) {
				mockRepo.On("CreateUserWithReferral", mock.Anything, mock.AnythingOfType("uuid.UUID"), "new-user-123").Return(nil)
			},
			expectedError: nil,
		},
		{
			name:       "referrer not found",
			referrerID: uuid.New(),
			userID:     "new-user-123",
			mockSetup: func(mockRepo *MockInvitationRepo) {
				mockRepo.On("CreateUserWithReferral", mock.Anything, mock.AnythingOfType("uuid.UUID"), "new-user-123").Return(errors.New("referrer not found"))
			},
			expectedError: errors.New("referrer not found"),
		},
		{
			name:       "user already exists",
			referrerID: uuid.New(),
			userID:     "existing-user",
			mockSetup: func(mockRepo *MockInvitationRepo) {
				mockRepo.On("CreateUserWithReferral", mock.Anything, mock.AnythingOfType("uuid.UUID"), "existing-user").Return(errors.New("user already exists"))
			},
			expectedError: errors.New("user already exists"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock repository
			mockRepo := &MockInvitationRepo{}
			tt.mockSetup(mockRepo)

			// Create service with mock repository
			service := NewMockInvitationService(mockRepo)

			// Execute test
			ctx := helper.CreateTestContext()
			err := service.CreateUserWithReferral(ctx, tt.referrerID, tt.userID)

			// Verify results
			if tt.expectedError != nil {
				helper.AssertError(err)
				helper.AssertEqual(tt.expectedError.Error(), err.Error())
			} else {
				helper.AssertNoError(err)
			}

			// Verify mock expectations
			mockRepo.AssertExpectations(t)
		})
	}
}

func TestInvitationCodeValidation(t *testing.T) {
	tests := []struct {
		name           string
		invitationCode string
		expectedValid  bool
	}{
		{
			name:           "valid invitation code",
			invitationCode: "VALID123",
			expectedValid:  true,
		},
		{
			name:           "invitation code with space",
			invitationCode: "INVALID 123",
			expectedValid:  false,
		},
		{
			name:           "invitation code with leading space",
			invitationCode: " INVALID123",
			expectedValid:  false,
		},
		{
			name:           "invitation code with trailing space",
			invitationCode: "INVALID123 ",
			expectedValid:  false,
		},
		{
			name:           "invitation code with tab",
			invitationCode: "INVALID\t123",
			expectedValid:  false,
		},
		{
			name:           "invitation code too short",
			invitationCode: "ABC1",
			expectedValid:  false,
		},
		{
			name:           "invitation code too long",
			invitationCode: "ABCDEFGHIJKLMNOP",
			expectedValid:  false,
		},
		{
			name:           "invitation code with special characters",
			invitationCode: "ABC@123",
			expectedValid:  true,
		},
		{
			name:           "invitation code with Chinese characters",
			invitationCode: "用户123",
			expectedValid:  true,
		},
		{
			name:           "invitation code with mixed characters",
			invitationCode: "USER用户",
			expectedValid:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test the validation function directly
			result := utils.IsValidInvitationCode(tt.invitationCode)
			if result != tt.expectedValid {
				t.Errorf("IsValidInvitationCode(%q) = %v, want %v", tt.invitationCode, result, tt.expectedValid)
			}
		})
	}
}
