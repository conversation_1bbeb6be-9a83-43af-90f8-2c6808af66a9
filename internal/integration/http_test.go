// +build integration

package integration

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestHTTPIntegration tests basic HTTP endpoint integration
func TestHTTPIntegration(t *testing.T) {
	t.Run("health check endpoint", func(t *testing.T) {
		// Create a simple health check handler
		handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"status":"ok","service":"xbit-agent"}`))
		})

		// Create test request
		req := httptest.NewRequest("GET", "/health", nil)
		w := httptest.NewRecorder()

		// Execute request
		handler.ServeHTTP(w, req)

		// Verify response
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "ok")
		assert.Contains(t, w.Body.String(), "xbit-agent")
	})

	t.Run("cors headers", func(t *testing.T) {
		// Create a handler that sets CORS headers
		handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Access-Control-Allow-Origin", "*")
			w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
			w.WriteHeader(http.StatusOK)
		})

		// Create test request
		req := httptest.NewRequest("OPTIONS", "/api/test", nil)
		w := httptest.NewRecorder()

		// Execute request
		handler.ServeHTTP(w, req)

		// Verify CORS headers
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))
		assert.Contains(t, w.Header().Get("Access-Control-Allow-Methods"), "GET")
		assert.Contains(t, w.Header().Get("Access-Control-Allow-Methods"), "POST")
		assert.Contains(t, w.Header().Get("Access-Control-Allow-Headers"), "Authorization")
	})

	t.Run("json content type", func(t *testing.T) {
		// Create a handler that returns JSON
		handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"message":"test response"}`))
		})

		// Create test request
		req := httptest.NewRequest("GET", "/api/test", nil)
		w := httptest.NewRecorder()

		// Execute request
		handler.ServeHTTP(w, req)

		// Verify response
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
		assert.Contains(t, w.Body.String(), "test response")
	})

	t.Run("authentication middleware simulation", func(t *testing.T) {
		// Create a middleware that checks for authorization header
		authMiddleware := func(next http.Handler) http.Handler {
			return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				authHeader := r.Header.Get("Authorization")
				if authHeader == "" {
					w.WriteHeader(http.StatusUnauthorized)
					w.Write([]byte(`{"error":"unauthorized"}`))
					return
				}
				next.ServeHTTP(w, r)
			})
		}

		// Create protected handler
		protectedHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"message":"authenticated"}`))
		})

		// Wrap with middleware
		handler := authMiddleware(protectedHandler)

		t.Run("without authorization header", func(t *testing.T) {
			req := httptest.NewRequest("GET", "/api/protected", nil)
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)

			assert.Equal(t, http.StatusUnauthorized, w.Code)
			assert.Contains(t, w.Body.String(), "unauthorized")
		})

		t.Run("with authorization header", func(t *testing.T) {
			req := httptest.NewRequest("GET", "/api/protected", nil)
			req.Header.Set("Authorization", "Bearer test-token")
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)

			assert.Equal(t, http.StatusOK, w.Code)
			assert.Contains(t, w.Body.String(), "authenticated")
		})
	})

	t.Run("error handling", func(t *testing.T) {
		// Create a handler that returns different error codes
		handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			switch r.URL.Path {
			case "/api/notfound":
				w.WriteHeader(http.StatusNotFound)
				w.Write([]byte(`{"error":"not found"}`))
			case "/api/badrequest":
				w.WriteHeader(http.StatusBadRequest)
				w.Write([]byte(`{"error":"bad request"}`))
			case "/api/servererror":
				w.WriteHeader(http.StatusInternalServerError)
				w.Write([]byte(`{"error":"internal server error"}`))
			default:
				w.WriteHeader(http.StatusOK)
				w.Write([]byte(`{"message":"ok"}`))
			}
		})

		testCases := []struct {
			path           string
			expectedStatus int
			expectedBody   string
		}{
			{"/api/notfound", http.StatusNotFound, "not found"},
			{"/api/badrequest", http.StatusBadRequest, "bad request"},
			{"/api/servererror", http.StatusInternalServerError, "internal server error"},
			{"/api/ok", http.StatusOK, "ok"},
		}

		for _, tc := range testCases {
			t.Run(tc.path, func(t *testing.T) {
				req := httptest.NewRequest("GET", tc.path, nil)
				w := httptest.NewRecorder()

				handler.ServeHTTP(w, req)

				assert.Equal(t, tc.expectedStatus, w.Code)
				assert.Contains(t, w.Body.String(), tc.expectedBody)
			})
		}
	})
}

// TestRequestValidation tests request validation scenarios
func TestRequestValidation(t *testing.T) {
	t.Run("validate request method", func(t *testing.T) {
		// Create a handler that only accepts POST requests
		handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if r.Method != http.MethodPost {
				w.WriteHeader(http.StatusMethodNotAllowed)
				w.Write([]byte(`{"error":"method not allowed"}`))
				return
			}
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"message":"success"}`))
		})

		// Test with GET request (should fail)
		req := httptest.NewRequest("GET", "/api/test", nil)
		w := httptest.NewRecorder()
		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusMethodNotAllowed, w.Code)

		// Test with POST request (should succeed)
		req = httptest.NewRequest("POST", "/api/test", nil)
		w = httptest.NewRecorder()
		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("validate content type", func(t *testing.T) {
		// Create a handler that only accepts JSON
		handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			contentType := r.Header.Get("Content-Type")
			if contentType != "application/json" {
				w.WriteHeader(http.StatusUnsupportedMediaType)
				w.Write([]byte(`{"error":"unsupported media type"}`))
				return
			}
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"message":"success"}`))
		})

		// Test with wrong content type
		req := httptest.NewRequest("POST", "/api/test", nil)
		req.Header.Set("Content-Type", "text/plain")
		w := httptest.NewRecorder()
		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusUnsupportedMediaType, w.Code)

		// Test with correct content type
		req = httptest.NewRequest("POST", "/api/test", nil)
		req.Header.Set("Content-Type", "application/json")
		w = httptest.NewRecorder()
		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
	})
}

// TestConcurrentRequests tests handling of concurrent requests
func TestConcurrentRequests(t *testing.T) {
	t.Run("concurrent requests", func(t *testing.T) {
		// Create a simple handler
		handler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"message":"ok"}`))
		})

		// Number of concurrent requests
		numRequests := 10
		results := make(chan int, numRequests)

		// Launch concurrent requests
		for i := 0; i < numRequests; i++ {
			go func() {
				req := httptest.NewRequest("GET", "/api/test", nil)
				w := httptest.NewRecorder()
				handler.ServeHTTP(w, req)
				results <- w.Code
			}()
		}

		// Collect results
		successCount := 0
		for i := 0; i < numRequests; i++ {
			statusCode := <-results
			if statusCode == http.StatusOK {
				successCount++
			}
		}

		// All requests should succeed
		assert.Equal(t, numRequests, successCount)
	})
}
