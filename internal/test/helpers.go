package test

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Define a custom type for context keys to avoid collisions
type contextKey string

const userIDKey contextKey = "userId"

// TestHelper provides common test utilities
type TestHelper struct {
	t *testing.T
}

// NewTestHelper creates a new test helper
func NewTestHelper(t *testing.T) *TestHelper {
	return &TestHelper{t: t}
}

// AssertNoError asserts that error is nil
func (h *TestHelper) AssertNoError(err error) {
	assert.NoError(h.t, err)
}

// AssertError asserts that error is not nil
func (h *TestHelper) AssertError(err error) {
	assert.Error(h.t, err)
}

// RequireNoError requires that error is nil
func (h *TestHelper) RequireNoError(err error) {
	require.NoError(h.t, err)
}

// RequireError requires that error is not nil
func (h *TestHelper) RequireError(err error) {
	require.Error(h.t, err)
}

// AssertEqual asserts that two values are equal
func (h *TestHelper) AssertEqual(expected, actual interface{}) {
	assert.Equal(h.t, expected, actual)
}

// AssertNotEqual asserts that two values are not equal
func (h *TestHelper) AssertNotEqual(expected, actual interface{}) {
	assert.NotEqual(h.t, expected, actual)
}

// AssertNil asserts that value is nil
func (h *TestHelper) AssertNil(value interface{}) {
	assert.Nil(h.t, value)
}

// AssertNotNil asserts that value is not nil
func (h *TestHelper) AssertNotNil(value interface{}) {
	assert.NotNil(h.t, value)
}

// AssertTrue asserts that value is true
func (h *TestHelper) AssertTrue(value bool) {
	assert.True(h.t, value)
}

// AssertFalse asserts that value is false
func (h *TestHelper) AssertFalse(value bool) {
	assert.False(h.t, value)
}

// AssertContains asserts that string contains substring
func (h *TestHelper) AssertContains(str, substr string) {
	assert.Contains(h.t, str, substr)
}

// AssertNotContains asserts that string does not contain substring
func (h *TestHelper) AssertNotContains(str, substr string) {
	assert.NotContains(h.t, str, substr)
}

// AssertLen asserts that object has expected length
func (h *TestHelper) AssertLen(object interface{}, length int) {
	assert.Len(h.t, object, length)
}

// AssertEmpty asserts that object is empty
func (h *TestHelper) AssertEmpty(object interface{}) {
	assert.Empty(h.t, object)
}

// AssertNotEmpty asserts that object is not empty
func (h *TestHelper) AssertNotEmpty(object interface{}) {
	assert.NotEmpty(h.t, object)
}

// CreateTestContext creates a test context
func (h *TestHelper) CreateTestContext() context.Context {
	return context.Background()
}

// CreateTestContextWithUserID creates a test context with user ID
func (h *TestHelper) CreateTestContextWithUserID(userID uuid.UUID) context.Context {
	ctx := context.Background()
	return context.WithValue(ctx, userIDKey, userID.String())
}

// CreateTestContextWithValue creates a test context with custom value
func (h *TestHelper) CreateTestContextWithValue(key, value interface{}) context.Context {
	ctx := context.Background()
	return context.WithValue(ctx, key, value)
}

// GenerateTestUUID generates a test UUID
func (h *TestHelper) GenerateTestUUID() uuid.UUID {
	return uuid.New()
}

// GenerateTestEmail generates a test email
func (h *TestHelper) GenerateTestEmail() string {
	return "test-" + uuid.New().String()[:8] + "@example.com"
}

// GenerateTestInvitationCode generates a test invitation code
func (h *TestHelper) GenerateTestInvitationCode() string {
	return "TEST" + uuid.New().String()[:6]
}

// StringToPointer converts string to pointer
func (h *TestHelper) StringToPointer(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}

// SetupTest performs common test setup
func SetupTest(t *testing.T) (*TestConfig, *TestFixtures, *TestHelper) {
	config := SetupTestConfig()
	fixtures := NewTestFixtures()
	helper := NewTestHelper(t)

	return config, fixtures, helper
}

// TeardownTest performs common test cleanup
func TeardownTest() {
	CleanupTestConfig()
}
