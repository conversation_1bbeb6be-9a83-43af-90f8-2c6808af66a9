package test

import (
	"os"
	"path/filepath"
	"runtime"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/config"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// TestConfig holds test-specific configuration
type TestConfig struct {
	*config.Server
}

// SetupTestConfig initializes test configuration
func SetupTestConfig() *TestConfig {
	// Get the project root directory
	_, filename, _, _ := runtime.Caller(0)
	projectRoot := filepath.Dir(filepath.Dir(filepath.Dir(filename)))

	testConfig := &TestConfig{
		Server: &config.Server{
			JWT: config.JWT{
				SigningKey:  "test-secret-key-for-unit-tests",
				ExpiresTime: "24h",
				BufferTime:  "1h",
				Issuer:      "xbit-agent-test",
			},
			System: config.System{
				Env:           "test",
				Addr:          8080,
				DbType:        "sqlite",
				OssType:       "local",
				UseMultipoint: false,
				UseRedis:      false,
				LimitCountIP:  15000,
				LimitTimeIP:   3600,
			},
			Zap: config.Zap{
				Level:         "info",
				Prefix:        "[xbit-agent-test]",
				Format:        "console",
				Director:      filepath.Join(projectRoot, "test", "logs"),
				EncodeLevel:   "LowercaseColorLevelEncoder",
				StacktraceKey: "stacktrace",
				LogInConsole:  true,
			},
			Redis: config.Redis{
				DB:       0,
				Addr:     "127.0.0.1:6379",
				Password: "",
			},
			Pgsql: config.Pgsql{
				GeneralDB: config.GeneralDB{
					Path:         ":memory:",
					Port:         "5432",
					Config:       "charset=utf8mb4&parseTime=True&loc=Local",
					Dbname:       "test_db",
					Username:     "test",
					Password:     "test",
					MaxIdleConns: 10,
					MaxOpenConns: 100,
					LogMode:      "info",
					LogZap:       true,
				},
			},
			NatsMeme: config.Nats{
				URL: "nats://localhost:4222",
			},
			NatsDex: config.Nats{
				URL: "nats://localhost:4222",
			},
		},
	}

	// Set global config for tests
	global.GVA_CONFIG = *testConfig.Server

	// Setup test logger
	setupTestLogger()

	return testConfig
}

// setupTestLogger initializes a test-friendly logger
func setupTestLogger() {
	// Create logs directory if it doesn't exist
	if err := os.MkdirAll(global.GVA_CONFIG.Zap.Director, 0755); err != nil {
		panic(err)
	}

	// Configure logger for tests
	config := zap.NewDevelopmentConfig()
	config.EncoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	config.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	config.Level = zap.NewAtomicLevelAt(zapcore.InfoLevel)

	logger, err := config.Build()
	if err != nil {
		panic(err)
	}

	global.GVA_LOG = logger
}

// CleanupTestConfig cleans up test resources
func CleanupTestConfig() {
	if global.GVA_LOG != nil {
		global.GVA_LOG.Sync()
	}
}
