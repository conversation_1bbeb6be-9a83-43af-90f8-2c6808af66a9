package test

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TestFixtures provides test data factories
type TestFixtures struct{}

// NewTestFixtures creates a new test fixtures instance
func NewTestFixtures() *TestFixtures {
	return &TestFixtures{}
}

// CreateTestUser creates a test user with default values
func (f *TestFixtures) CreateTestUser() *model.User {
	userID := uuid.New()
	email := "<EMAIL>"
	invitationCode := "TEST123"

	return &model.User{
		ID:             userID,
		Email:          &email,
		InvitationCode: &invitationCode,
		AgentLevelID:   1,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}
}

// CreateTestUserWithID creates a test user with specific ID
func (f *TestFixtures) CreateTestUserWithID(userID uuid.UUID) *model.User {
	email := "<EMAIL>"
	invitationCode := "TEST123"

	return &model.User{
		ID:             userID,
		Email:          &email,
		InvitationCode: &invitationCode,
		AgentLevelID:   1,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}
}

// CreateTestUserWithEmail creates a test user with specific email
func (f *TestFixtures) CreateTestUserWithEmail(email string) *model.User {
	userID := uuid.New()
	invitationCode := "TEST123"

	return &model.User{
		ID:             userID,
		Email:          &email,
		InvitationCode: &invitationCode,
		AgentLevelID:   1,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}
}

// CreateTestAgentLevel creates a test agent level
func (f *TestFixtures) CreateTestAgentLevel() *model.AgentLevel {
	return &model.AgentLevel{
		ID:                      1,
		Name:                    "Lv1",
		MemeVolumeThreshold:     decimal.NewFromFloat(1000.0),
		ContractVolumeThreshold: decimal.NewFromFloat(5000.0),
		MemeFeeRate:             decimal.NewFromFloat(0.008),
		TakerFeeRate:            decimal.NewFromFloat(0.0055),
		MakerFeeRate:            decimal.NewFromFloat(0.0025),
		DirectCommissionRate:    decimal.NewFromFloat(0.30),
		IndirectCommissionRate:  decimal.NewFromFloat(0.05),
		ExtendedCommissionRate:  decimal.NewFromFloat(0.025),
		ActivityCashbackRate:    decimal.NewFromFloat(0.25),
	}
}

// CreateTestAgentLevelWithID creates a test agent level with specific ID
func (f *TestFixtures) CreateTestAgentLevelWithID(id uint, name string) *model.AgentLevel {
	return &model.AgentLevel{
		ID:                      id,
		Name:                    name,
		MemeVolumeThreshold:     decimal.NewFromFloat(1000.0 * float64(id)),
		ContractVolumeThreshold: decimal.NewFromFloat(5000.0 * float64(id)),
		MemeFeeRate:             decimal.NewFromFloat(0.008 - (0.001 * float64(id-1))),
		TakerFeeRate:            decimal.NewFromFloat(0.0055),
		MakerFeeRate:            decimal.NewFromFloat(0.0025),
		DirectCommissionRate:    decimal.NewFromFloat(0.30 + (0.05 * float64(id-1))),
		IndirectCommissionRate:  decimal.NewFromFloat(0.05 + (0.01 * float64(id-1))),
		ExtendedCommissionRate:  decimal.NewFromFloat(0.025 + (0.005 * float64(id-1))),
		ActivityCashbackRate:    decimal.NewFromFloat(0.25 + (0.05 * float64(id-1))),
	}
}

// CreateTestReferral creates a test referral relationship
func (f *TestFixtures) CreateTestReferral(userID, referrerID uuid.UUID, depth int) *model.Referral {
	return &model.Referral{
		ID:         1,
		UserID:     userID,
		ReferrerID: &referrerID,
		Depth:      depth,
		CreatedAt:  time.Now(),
	}
}

// CreateTestReferralSnapshot creates a test referral snapshot
func (f *TestFixtures) CreateTestReferralSnapshot(userID uuid.UUID) *model.ReferralSnapshot {
	return &model.ReferralSnapshot{
		UserID:                  userID,
		DirectCount:             5,
		TotalDownlineCount:      15,
		TotalVolumeUSD:          decimal.NewFromFloat(50000.0),
		TotalRewardsDistributed: decimal.NewFromFloat(1500.0),
		L1UplineID:              nil,
		L2UplineID:              nil,
		L3UplineID:              nil,
	}
}

// CreateTestAffiliateTransaction creates a test affiliate transaction
func (f *TestFixtures) CreateTestAffiliateTransaction() *model.AffiliateTransaction {
	userID := uuid.New()
	return &model.AffiliateTransaction{
		ID:              1,
		OrderID:         uuid.New(),
		UserID:          userID,
		UserAddress:     "test_address_123",
		TxHash:          "test_tx_hash_123",
		TransactionType: model.Buy,
		Type:            model.Market,
		ChainID:         "solana",
		BaseAddress:     "0x123456789",
		BaseSymbol:      "TEST",
		QuoteAddress:    "0x987654321",
		QuoteSymbol:     "USDC",
		BaseAmount:      decimal.NewFromFloat(100.0),
		QuoteAmount:     decimal.NewFromFloat(1000.0),
		TotalFee:        decimal.NewFromFloat(8.0),
		Slippage:        decimal.NewFromFloat(0.01),
		Status:          model.StatusCompleted,
		VolumeUSD:       decimal.NewFromFloat(1000.0),
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}
}

// CreateTestCommissionLedger creates a test commission ledger entry
func (f *TestFixtures) CreateTestCommissionLedger(userID uuid.UUID) *model.CommissionLedger {
	sourceUserID := uuid.New()
	now := time.Now()
	return &model.CommissionLedger{
		ID:                    uuid.New(),
		RecipientUserID:       userID,
		SourceUserID:          sourceUserID,
		SourceTransactionID:   "test_tx_123",
		SourceTransactionType: "Direct",
		CommissionAmount:      decimal.NewFromFloat(30.0),
		CommissionAsset:       "USDC",
		Status:                "PENDING_CLAIM",
		CreatedAt:             &now,
		UpdatedAt:             &now,
	}
}

// CreateTestDailyUserVolume creates a test daily user volume entry
func (f *TestFixtures) CreateTestDailyUserVolume(userID uuid.UUID, date time.Time) *model.DailyUserVolume {
	return &model.DailyUserVolume{
		ID:                uuid.New(),
		UserID:            userID,
		Date:              date,
		MemeVolumeUSD:     decimal.NewFromFloat(5000.0),
		ContractVolumeUSD: decimal.NewFromFloat(10000.0),
	}
}

// CreateMultipleTestUsers creates multiple test users
func (f *TestFixtures) CreateMultipleTestUsers(count int) []*model.User {
	users := make([]*model.User, count)
	for i := 0; i < count; i++ {
		users[i] = f.CreateTestUser()
	}
	return users
}

// CreateMultipleTestAgentLevels creates multiple test agent levels
func (f *TestFixtures) CreateMultipleTestAgentLevels(count int) []*model.AgentLevel {
	levels := make([]*model.AgentLevel, count)
	for i := 0; i < count; i++ {
		levels[i] = f.CreateTestAgentLevelWithID(uint(i+1), "Lv"+string(rune('1'+i)))
	}
	return levels
}
