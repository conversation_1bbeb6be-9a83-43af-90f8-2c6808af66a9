Server = .
ServerName = xbit-agent

# Define variables
GOOS = linux   # Target OS (can be overridden)
GOARCH = amd64 # Target architecture
BinDir = ./bin

# Detect local platform for development
LOCAL_GOOS = $(shell go env GOOS)
LOCAL_GOARCH = $(shell go env GOARCH)

build:
	mkdir -p $(BinDir)
	cd $(Server) && GOOS=$(GOOS) GOARCH=$(GOARCH) go build -o $(BinDir)/$(ServerName) cmd/graphql/main.go
	@echo "Server built for $(GOOS)/$(GOARCH) in $(BinDir)"

build-local:
	mkdir -p $(BinDir)
	cd $(Server) && GOOS=$(LOCAL_GOOS) GOARCH=$(LOCAL_GOARCH) go build -o $(BinDir)/$(ServerName) cmd/graphql/main.go
	@echo "Server built for $(LOCAL_GOOS)/$(LOCAL_GOARCH) in $(BinDir)"

db-diff:
	# atlas migrate diff [flags] [name]
	atlas migrate diff --env gorm

db-rehash:
	atlas migrate hash --dir file://migrations

db-fix-checksum:
	./scripts/fix-migration-checksum.sh

db-apply:
	./scripts/run.sh local migrate

db-apply-docker:
	./scripts/run.sh docker migrate

# Atlas migrations
db-apply-atlas:
	@if [ -z "$$DATABASE_URL" ]; then \
		echo "Error: DATABASE_URL environment variable is not set"; \
		echo "Please set DATABASE_URL or use 'make db-apply' instead"; \
		exit 1; \
	fi
	atlas migrate apply --url "$$DATABASE_URL" --dir file://migrations

db-apply-atlas-docker:
	atlas migrate apply --url "postgres://postgres:postgres@localhost:5432/agent?sslmode=disable" --dir file://migrations

gqlgen:
	go run github.com/99designs/gqlgen generate --config gqlgen.yml

install-deps:
	go mod tidy

run:
	go run cmd/graphql/main.go

run-local:
	./scripts/run.sh local run

run-docker:
	docker-compose up -d

dev:
	./scripts/run.sh local dev

dev-air:
	air -c .air.toml

test:
	go test ./...

test-verbose:
	go test -v ./...

test-coverage:
	go test -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

test-unit:
	go test -v ./internal/utils/... ./internal/service/... ./internal/controller/...

test-integration:
	go test -v -tags=integration ./...

test-watch:
	@echo "Running tests in watch mode (requires entr)..."
	find . -name "*.go" | entr -c go test ./...

clean:
	rm -rf $(BinDir)

# JWT Token Generation for Testing
jwt-token:
	@echo "Generating JWT token for local testing..."
	./scripts/generate-jwt.sh

jwt-token-help:
	./scripts/generate-jwt.sh --help

jwt-token-custom:
	@echo "Usage: make jwt-token-custom USER_ID=<uuid> EMAIL=<email>"
	@if [ -z "$(USER_ID)" ] && [ -z "$(EMAIL)" ]; then \
		echo "Example: make jwt-token-custom EMAIL=<EMAIL>"; \
		echo "Example: make jwt-token-custom USER_ID=123e4567-e89b-12d3-a456-************ EMAIL=<EMAIL>"; \
		./scripts/generate-jwt.sh; \
	else \
		./scripts/generate-jwt.sh $(if $(USER_ID),-u $(USER_ID)) $(if $(EMAIL),-e $(EMAIL)); \
	fi

jwt-token-test:
	@echo "Testing JWT token against GraphQL endpoint..."
	./scripts/test-jwt-token.sh

.PHONY: build build-local db-diff db-rehash db-fix-checksum db-apply db-apply-docker db-apply-atlas db-apply-atlas-docker gqlgen install-deps run dev test test-verbose test-coverage test-unit test-integration test-watch clean jwt-token jwt-token-help jwt-token-custom jwt-token-test
